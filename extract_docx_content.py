#!/usr/bin/env python3
"""
Script to extract content from a Word document and create a PowerPoint presentation
"""

import os
import sys
from docx import Document
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor

def extract_docx_content(docx_path):
    """Extract text content from a Word document"""
    try:
        doc = Document(docx_path)
        content = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:  # Only add non-empty paragraphs
                # Check if this might be a heading based on formatting
                is_heading = False
                if paragraph.style.name.startswith('Heading'):
                    is_heading = True
                elif len(text) < 100 and text.endswith(':'):
                    is_heading = True
                elif text.isupper() and len(text) < 50:
                    is_heading = True
                
                content.append({
                    'text': text,
                    'is_heading': is_heading,
                    'style': paragraph.style.name
                })
        
        return content
    except Exception as e:
        print(f"Error reading Word document: {e}")
        return []

def create_powerpoint(content, output_path):
    """Create a PowerPoint presentation from extracted content"""
    try:
        # Create a new presentation
        prs = Presentation()
        
        # Title slide
        title_slide_layout = prs.slide_layouts[0]  # Title slide layout
        slide = prs.slides.add_slide(title_slide_layout)
        title = slide.shapes.title
        subtitle = slide.placeholders[1]
        
        title.text = "Classroom Organization and Management"
        subtitle.text = "Best Practices and Strategies"
        
        current_slide = None
        current_content = []
        
        for item in content:
            text = item['text']
            is_heading = item['is_heading']
            
            # If this is a heading, create a new slide
            if is_heading:
                # Save previous slide content if exists
                if current_slide is not None and current_content:
                    add_content_to_slide(current_slide, current_content)
                
                # Create new slide
                slide_layout = prs.slide_layouts[1]  # Title and content layout
                current_slide = prs.slides.add_slide(slide_layout)
                current_slide.shapes.title.text = text
                current_content = []
            else:
                # Add to current content
                if text:
                    current_content.append(text)
        
        # Add final slide content
        if current_slide is not None and current_content:
            add_content_to_slide(current_slide, current_content)
        
        # Save the presentation
        prs.save(output_path)
        print(f"PowerPoint presentation saved as: {output_path}")
        
    except Exception as e:
        print(f"Error creating PowerPoint: {e}")

def add_content_to_slide(slide, content_list):
    """Add bullet points to a slide"""
    try:
        content_placeholder = slide.placeholders[1]
        text_frame = content_placeholder.text_frame
        text_frame.clear()
        
        for i, content in enumerate(content_list[:8]):  # Limit to 8 bullet points per slide
            if i == 0:
                p = text_frame.paragraphs[0]
            else:
                p = text_frame.add_paragraph()
            
            p.text = content
            p.level = 0
            
    except Exception as e:
        print(f"Error adding content to slide: {e}")

def main():
    docx_file = "Classroom Organization and management.docx"
    pptx_file = "Classroom_Organization_and_Management.pptx"
    
    if not os.path.exists(docx_file):
        print(f"Error: {docx_file} not found!")
        return
    
    print(f"Extracting content from {docx_file}...")
    content = extract_docx_content(docx_file)
    
    if not content:
        print("No content extracted from the document!")
        return
    
    print(f"Found {len(content)} paragraphs. Creating PowerPoint...")
    create_powerpoint(content, pptx_file)
    
    print("Done!")

if __name__ == "__main__":
    main()
