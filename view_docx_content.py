#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to view the content structure of the Word document
"""

from docx import Document
import os

def analyze_docx_content(docx_path):
    """Analyze and display the structure of a Word document"""
    try:
        doc = Document(docx_path)
        print(f"Analyzing: {docx_path}")
        print("=" * 50)
        
        paragraph_count = 0
        heading_count = 0
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text:  # Only process non-empty paragraphs
                paragraph_count += 1
                style_name = paragraph.style.name
                
                # Determine if it's likely a heading
                is_heading = False
                if style_name.startswith('Heading'):
                    is_heading = True
                    heading_count += 1
                elif len(text) < 100 and (text.endswith(':') or text.isupper()):
                    is_heading = True
                    heading_count += 1
                
                # Display paragraph info
                marker = "📍 HEADING" if is_heading else "📝 Content"
                print(f"{marker} [{style_name}]: {text[:80]}{'...' if len(text) > 80 else ''}")
        
        print("=" * 50)
        print(f"Summary:")
        print(f"- Total paragraphs with content: {paragraph_count}")
        print(f"- Identified headings: {heading_count}")
        print(f"- Estimated slides: {heading_count + 1}")  # +1 for title slide
        
    except Exception as e:
        print(f"Error analyzing document: {e}")

def main():
    docx_file = "Classroom Organization and management.docx"
    
    if not os.path.exists(docx_file):
        print(f"Error: {docx_file} not found!")
        return
    
    analyze_docx_content(docx_file)

if __name__ == "__main__":
    main()
